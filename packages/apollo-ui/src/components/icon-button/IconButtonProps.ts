import type { Ref } from "react"

import type {
  BaseButtonProps,
  LinkButtonProps,
  NormalButtonProps,
} from "../button"

export type IconButtonSize = "small" | "large"

export type IconButtonBaseProps = {
  ref?: Ref<HTMLButtonElement>
  size?: IconButtonSize
} & Pick<BaseButtonProps, "color" | "variant">

export type IconButtonProps = (
  | ({ href: string } & LinkButtonProps)
  | ({
      href?: never
    } & NormalButtonProps)
) &
  IconButtonBaseProps
